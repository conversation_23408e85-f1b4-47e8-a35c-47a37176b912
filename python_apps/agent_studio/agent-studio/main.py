import os
import uuid
import dotenv
import fastapi
from datetime import datetime
from starlette.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

# Use local imports
from agents import agent_schemas
from agents.command_agent import CommandAgent
from prompts import command_agent_system_prompt
from db.database import Base, engine, get_db
from db.init_db import init_db
from api import prompt_router, agent_router, demo_router, workflow_router
from db import crud
from services.workflow_service import workflow_service

from contextlib import asynccontextmanager

# # Add the current directory to the Python path
# current_dir = os.path.dirname(os.path.abspath(__file__))
# if current_dir not in sys.path:
#     sys.path.append(current_dir)

dotenv.load_dotenv(".env.local")

CX_ID = os.getenv("CX_ID_PERSONAL")
GOOGLE_API = os.getenv("GOOGLE_API_PERSONAL")

COMMAND_AGENT = None

origins = ["http://127.0.0.1:3002", "*"]


@asynccontextmanager
async def lifespan(app_instance: fastapi.FastAPI):
    Base.metadata.create_all(bind=engine)
    init_db()
    yield

    pass


app = fastapi.FastAPI(title="Agent Studio Backend", version="0.1.0", lifespan=lifespan)

app.include_router(prompt_router)
app.include_router(agent_router)
app.include_router(demo_router)
app.include_router(workflow_router)


@app.get("/")
def status():
    return {"status": "ok"}


@app.get("/hc")
def health_check():
    return {"status": "ok"}


@app.get("/deployment/codes")
def get_deployment_codes(db: Session = fastapi.Depends(get_db)):
    """
    Get a mapping of deployment codes to agent names.
    This endpoint is used by the frontend to display available agents.
    """
    available_agents = crud.get_available_agents(db)

    code_map = {}
    for agent in available_agents:
        deployment_code = getattr(agent, "deployment_code", None)
        if deployment_code is not None and str(deployment_code).strip() != "":
            code_map[str(deployment_code)] = str(agent.name)

    return code_map


@app.get("/agents/available")
def get_available_agents_modern(db: Session = fastapi.Depends(get_db)):
    """Get all available agents for modern workflow creation"""
    agents = crud.get_available_agents(db)
    return [
        {
            "agent_id": str(agent.agent_id),
            "name": agent.name,
            "agent_type": agent.agent_type,
            "description": agent.description,
            "deployment_code": agent.deployment_code,
        }
        for agent in agents
    ]


@app.post("/deploy")
def deploy(request: dict, db: Session = fastapi.Depends(get_db)):
    """
    Modern workflow deployment endpoint
    Expected request format:
    {
        "workflow_name": "My Workflow",
        "description": "Description of what this workflow does",
        "agents": [
            {"agent_id": "uuid-1", "position": {"x": 100, "y": 50}},
            {"agent_id": "uuid-2", "position": {"x": 300, "y": 150}}
        ],
        "connections": [
            {
                "source_agent_id": "uuid-1",
                "target_agent_id": "uuid-2",
                "connection_type": "default"
            }
        ]
    }
    """
    global COMMAND_AGENT
    print("Deploy request:", request)

    try:
        # Extract workflow information
        workflow_name = request.get("workflow_name", f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        description = request.get("description", "Workflow created via deploy endpoint")
        agents_data = request.get("agents", [])
        connections_data = request.get("connections", [])

        # Create workflow
        workflow = workflow_service.create_modern_workflow(
            db=db,
            name=workflow_name,
            description=description,
            agents_data=agents_data,
            connections_data=connections_data,
            created_by="api_user",
        )

        # Deploy the workflow
        deployment_name = f"deployment_{workflow.workflow_id}"
        deployment = workflow_service.deploy_workflow(
            db=db,
            workflow_id=workflow.workflow_id,
            deployment_name=deployment_name,
            environment="production",
            deployed_by="api_user",
        )

        # Set the global COMMAND_AGENT for immediate use
        active_deployments = workflow_service.get_active_deployments()
        if deployment_name in active_deployments:
            COMMAND_AGENT = active_deployments[deployment_name]["command_agent"]

        return {
            "status": "success",
            "message": "Workflow deployed successfully",
            "workflow_id": str(workflow.workflow_id),
            "deployment_id": str(deployment.deployment_id),
            "deployment_name": deployment_name,
        }

    except Exception as e:
        print(f"Deploy error: {e}")
        return {"status": "error", "message": f"Failed to deploy workflow: {str(e)}"}


@app.post("/run")
def run(request: dict):
    if COMMAND_AGENT is not None:
        res = COMMAND_AGENT.execute(query=request["query"])

        return {"status": "ok", "response": f"{res}"}


@app.post("/run_stream")
def run_stream(request: dict):
    chat_history = request["chat_history"]
    print("Chat History: ", chat_history)
    print("*" * 10)
    gpt_chat_history = ""

    for i in chat_history[:-1]:
        gpt_chat_history += f"{i['actor']}: {i['content']}\n"

    async def response_stream():
        if COMMAND_AGENT is not None:
            for chunk in COMMAND_AGENT.execute_stream(request["query"], gpt_chat_history):
                yield chunk
            return

    return fastapi.responses.StreamingResponse(response_stream(), media_type="text/event-stream")


if __name__ == "__main__":
    import uvicorn

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["Content-Type", "Authorization"],
    )

    uvicorn.run(app, host="localhost", port=8000)
