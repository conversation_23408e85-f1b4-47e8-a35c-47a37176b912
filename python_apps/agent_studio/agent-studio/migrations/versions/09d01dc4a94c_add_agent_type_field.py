"""Add agent_type field

Revision ID: 09d01dc4a94c
Revises: 4ff5ac1965c1
Create Date: 2025-05-28 18:59:15.177326

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '09d01dc4a94c'
down_revision: Union[str, None] = '4ff5ac1965c1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('agent_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agents', 'agent_type')
    # ### end Alembic commands ###
