"""Add description field to agents

Revision ID: d2d4d2e688c4
Revises: 09d01dc4a94c
Create Date: 2025-05-28 19:41:52.741421

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd2d4d2e688c4'
down_revision: Union[str, None] = '09d01dc4a94c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('agents', sa.Column('description', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('agents', 'description')
    # ### end Alembic commands ###
